{"training_data": {"cascade_count": 57, "non_cascade_count": 2, "minority_ratio": 0.03389830508474576, "total_samples": 67, "feature_count": 7}, "metrics": {"out_of_time_accuracy": 1.0, "train_accuracy": 0.9811320754716981, "accuracy_ci_lower": 0.8975694709571569, "accuracy_ci_upper": 1.043089869702184, "cv_mean": 0.9703296703296704, "cv_std": 0.036380099686256794, "temporal_split": "80/20"}, "model_details": {"model_type": "XGBoost", "feature_names": ["grammatical_event_density", "pattern_completion_probability", "cross_session_influence_score", "fpfvg_interaction_strength", "session_type_encoded", "pattern_sequence_complexity", "cascade_trigger_proximity"], "confusion_matrix": [[8, 0], [0, 6]], "classification_report": {"0": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 8.0}, "1": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 6.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}}}, "ablations": {"temporal_features_delta": -0.29850746268656714, "price_features_delta": 0.0, "cascade_features_delta": 0.0, "metadata_features_delta": 0.0}, "scaler": "StandardScaler()", "training_time": 0.20939087867736816, "notes": ["Real XGBoost training on enhanced sessions", "Trained on 67 samples with 7 features"]}