{"generated_by": "run_compartments.py", "timestamp": "{\"timestamp\": \"now\"}", "artifacts": [{"compartment": "ml_update", "output": {"compartment": "ml_update", "artifacts": {"models_dir": "models", "model_file": "models/trained_model.pkl", "training_report": "models/training_report.json", "promoted": true, "model_type": "xgboost"}, "runtime_seconds": 0.254, "promoted": true, "status": "PROMOTED"}}]}