"""
VQE-Inspired Optimization Shell Package

Production implementation of Gemini's validated VQE approach using COBYLA algorithm.
Provides optimization capabilities for the Oracle prediction system.
"""

from .optimization_shell import (
    VQEOptimizationShell,
    create_vqe_optimization_shell,
    OptimizationResult,
    OptimizationConfig
)

__version__ = "1.0.0"
__all__ = [
    "VQEOptimizationShell",
    "create_vqe_optimization_shell", 
    "OptimizationResult",
    "OptimizationConfig"
]
